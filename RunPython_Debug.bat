@echo off
REM 调试版本 - 用于诊断计划任务问题
echo ========== 调试信息 ==========
echo 脚本完整路径: %~f0
echo 脚本所在目录: %~dp0
echo 当前工作目录: %CD%
echo 用户名: %USERNAME%
echo 计算机名: %COMPUTERNAME%
echo 当前时间: %date% %time%
echo ================================

REM 切换到脚本目录
echo 正在切换到脚本目录...
cd /d "%~dp0"
echo 切换后的工作目录: %CD%

REM 检查关键文件
echo.
echo 检查关键文件:
if exist "send_email.py" (
    echo ✓ send_email.py 存在
) else (
    echo ✗ send_email.py 不存在
)

if exist "APPLE" (
    echo ✓ APPLE 目录存在
) else (
    echo ✗ APPLE 目录不存在
)

REM 检查Python
echo.
echo 检查Python:
if exist "D:\Program Files\Python\python.exe" (
    echo ✓ Python可执行文件存在
    "D:\Program Files\Python\python.exe" --version
    if errorlevel 1 (
        echo ✗ Python版本检查失败
    ) else (
        echo ✓ Python版本检查成功
    )
) else (
    echo ✗ Python可执行文件不存在
)

REM 创建简单日志
echo.
echo 创建日志目录...
if not exist "logs" mkdir "logs"
set SIMPLE_LOG=logs\debug_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log
set SIMPLE_LOG=%SIMPLE_LOG: =0%

echo 日志文件: %SIMPLE_LOG%
echo 调试开始时间: %date% %time% > "%SIMPLE_LOG%"
echo 工作目录: %CD% >> "%SIMPLE_LOG%"

REM 询问是否继续执行Python脚本
echo.
set /p CONTINUE=是否继续执行Python脚本? (y/n): 
if /i "%CONTINUE%"=="y" (
    echo.
    echo 执行Python脚本...
    echo 执行开始: %date% %time% >> "%SIMPLE_LOG%"
    
    "D:\Program Files\Python\python.exe" "send_email.py" 2>&1 | tee -a "%SIMPLE_LOG%"
    
    echo 执行结束: %date% %time% >> "%SIMPLE_LOG%"
    echo Python脚本退出码: %errorlevel% >> "%SIMPLE_LOG%"
    
    if errorlevel 1 (
        echo.
        echo Python脚本执行失败，退出码: %errorlevel%
    ) else (
        echo.
        echo Python脚本执行成功！
    )
) else (
    echo 跳过Python脚本执行
)

echo.
echo 调试完成，日志保存在: %SIMPLE_LOG%
echo 按任意键退出...
pause >nul
