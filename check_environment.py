#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查和修复脚本
用于诊断计划任务执行时的环境问题
"""

import os
import sys
import subprocess
import platform
import json
from datetime import datetime

def log_message(message):
    """记录日志消息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_python_environment():
    """检查Python环境"""
    log_message("=== Python环境检查 ===")
    log_message(f"Python版本: {sys.version}")
    log_message(f"Python路径: {sys.executable}")
    log_message(f"当前工作目录: {os.getcwd()}")
    log_message(f"脚本所在目录: {os.path.dirname(os.path.abspath(__file__))}")
    
    # 检查必要的模块
    required_modules = ['selenium', 'webdriver_manager', 'smtplib', 'email']
    log_message("\n=== 模块检查 ===")
    
    for module in required_modules:
        try:
            __import__(module)
            log_message(f"✓ {module} - 可用")
        except ImportError as e:
            log_message(f"✗ {module} - 不可用: {e}")

def check_chrome_environment():
    """检查Chrome环境"""
    log_message("\n=== Chrome环境检查 ===")
    
    # 检查Chrome是否安装
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
    ]
    
    chrome_found = False
    for path in chrome_paths:
        expanded_path = os.path.expandvars(path)
        if os.path.exists(expanded_path):
            log_message(f"✓ Chrome找到: {expanded_path}")
            chrome_found = True
            break
    
    if not chrome_found:
        log_message("✗ Chrome未找到")
    
    # 检查ChromeDriver
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        driver_path = ChromeDriverManager().install()
        log_message(f"✓ ChromeDriver路径: {driver_path}")
    except Exception as e:
        log_message(f"✗ ChromeDriver检查失败: {e}")

def check_file_permissions():
    """检查文件权限"""
    log_message("\n=== 文件权限检查 ===")
    
    # 检查关键目录
    directories = ['APPLE', 'HUAWEI', 'OPPO', 'VIVO', 'XIAOMI', 'HONOR', 'INFO']
    
    for directory in directories:
        if os.path.exists(directory):
            if os.access(directory, os.R_OK | os.W_OK):
                log_message(f"✓ {directory} - 读写权限正常")
            else:
                log_message(f"✗ {directory} - 权限不足")
        else:
            log_message(f"⚠ {directory} - 目录不存在")

def check_network_connectivity():
    """检查网络连接"""
    log_message("\n=== 网络连接检查 ===")
    
    test_urls = [
        "https://apps.apple.com",
        "https://appgallery.huawei.com",
        "https://store.oppo.com"
    ]
    
    for url in test_urls:
        try:
            import urllib.request
            urllib.request.urlopen(url, timeout=10)
            log_message(f"✓ {url} - 连接正常")
        except Exception as e:
            log_message(f"✗ {url} - 连接失败: {e}")

def check_system_resources():
    """检查系统资源"""
    log_message("\n=== 系统资源检查 ===")
    
    # 检查内存使用
    try:
        import psutil
        memory = psutil.virtual_memory()
        log_message(f"内存使用: {memory.percent}% ({memory.used // (1024**3)}GB / {memory.total // (1024**3)}GB)")
        
        # 检查磁盘空间
        disk = psutil.disk_usage('.')
        log_message(f"磁盘使用: {disk.percent}% ({disk.used // (1024**3)}GB / {disk.total // (1024**3)}GB)")
        
    except ImportError:
        log_message("psutil模块未安装，无法检查系统资源")

def test_chrome_startup():
    """测试Chrome启动"""
    log_message("\n=== Chrome启动测试 ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        # 配置Chrome选项（计划任务优化）
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--remote-debugging-port=9222')
        
        # 创建WebDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 测试访问一个简单页面
        driver.get("https://www.baidu.com")
        title = driver.title
        log_message(f"✓ Chrome启动成功，页面标题: {title}")
        
        driver.quit()
        
    except Exception as e:
        log_message(f"✗ Chrome启动失败: {e}")

def generate_report():
    """生成环境检查报告"""
    log_message("\n=== 生成环境检查报告 ===")
    
    report = {
        "check_time": datetime.now().isoformat(),
        "platform": platform.platform(),
        "python_version": sys.version,
        "working_directory": os.getcwd(),
        "environment_variables": dict(os.environ)
    }
    
    # 保存报告
    report_file = f"environment_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    log_message(f"环境检查报告已保存: {report_file}")

def main():
    """主函数"""
    log_message("开始环境检查...")
    
    check_python_environment()
    check_chrome_environment()
    check_file_permissions()
    check_network_connectivity()
    check_system_resources()
    test_chrome_startup()
    generate_report()
    
    log_message("\n环境检查完成！")

if __name__ == "__main__":
    main()
