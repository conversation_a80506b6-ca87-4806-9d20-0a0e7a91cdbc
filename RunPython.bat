@echo off
cd /d "%~dp0"

REM 设置环境变量以支持计划任务执行
set PYTHONIOENCODING=utf-8
set PYTHONUNBUFFERED=1

REM 记录开始时间
echo [%date% %time%] Starting Python automation script...

REM 创建日志目录
if not exist "logs" mkdir logs

REM 执行Python脚本并记录日志
echo Running Python script...
"D:\Program Files\Python\python.exe" "%~dp0send_email.py" > "logs\automation_%date:~0,4%-%date:~5,2%-%date:~8,2%.log" 2>&1

REM 检查执行结果
if errorlevel 1 (
    echo [%date% %time%] Error occurred while running Python script
    echo Check log file: logs\automation_%date:~0,4%-%date:~5,2%-%date:~8,2%.log
    REM 计划任务中不使用pause
    if "%1"=="scheduled" (
        exit /b 1
    ) else (
        pause
        exit /b 1
    )
)

echo [%date% %time%] <PERSON><PERSON><PERSON> completed successfully
REM 计划任务中不使用pause
if "%1"=="scheduled" (
    exit /b 0
) else (
    pause
)