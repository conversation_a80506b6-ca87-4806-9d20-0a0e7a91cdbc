@echo off
REM 专门用于Windows计划任务的Python自动化脚本启动器
REM 解决计划任务中Chrome WebDriver启动问题

REM 启用延迟变量扩展
setlocal enabledelayedexpansion

REM 强制切换到脚本所在目录（解决路径问题）
echo 脚本路径: %~dp0
cd /d "%~dp0"
echo 当前工作目录: %CD%

REM 验证是否在正确目录
if not exist "send_email.py" (
    echo 错误：未找到send_email.py文件
    echo 当前目录：%CD%
    echo 脚本目录：%~dp0
    echo 请检查脚本是否在正确位置运行
    pause
    exit /b 1
)

REM 设置关键环境变量
set PYTHONIOENCODING=utf-8
set PYTHONUNBUFFERED=1
set DISPLAY=:0
set CHROME_NO_SANDBOX=1

REM 获取当前日期时间（兼容性更好的方式）
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%-%MM%-%DD%" & set "timestamp=%HH%-%Min%-%Sec%"

REM 设置日志文件路径
set LOG_DIR=%~dp0logs
set LOG_FILE=%LOG_DIR%\scheduled_task_%datestamp%_%timestamp%.log

REM 创建日志目录
if not exist "%LOG_DIR%" (
    mkdir "%LOG_DIR%"
    if errorlevel 1 (
        echo 无法创建日志目录: %LOG_DIR%
        echo 按任意键继续...
        pause >nul
        exit /b 1
    )
)

REM 测试日志文件写入权限
echo 测试日志写入... > "%LOG_FILE%" 2>&1
if errorlevel 1 (
    echo 无法写入日志文件: %LOG_FILE%
    echo 按任意键继续...
    pause >nul
    exit /b 1
)

REM 记录开始时间和环境信息
echo [%datestamp% %HH%:%Min%:%Sec%] ========== 计划任务开始执行 ========== >> "%LOG_FILE%"
echo [%datestamp% %HH%:%Min%:%Sec%] 脚本路径: %~dp0 >> "%LOG_FILE%"
echo [%datestamp% %HH%:%Min%:%Sec%] 工作目录: %CD% >> "%LOG_FILE%"
echo [%datestamp% %HH%:%Min%:%Sec%] 用户名: %USERNAME% >> "%LOG_FILE%"
echo [%datestamp% %HH%:%Min%:%Sec%] 计算机名: %COMPUTERNAME% >> "%LOG_FILE%"
echo [%datestamp% %HH%:%Min%:%Sec%] Python路径: "D:\Program Files\Python\python.exe" >> "%LOG_FILE%"

REM 检查Python文件是否存在
if not exist "D:\Program Files\Python\python.exe" (
    echo [%datestamp% %HH%:%Min%:%Sec%] ERROR: Python可执行文件不存在 >> "%LOG_FILE%"
    echo Python可执行文件不存在: "D:\Program Files\Python\python.exe"
    echo 按任意键继续...
    pause >nul
    exit /b 1
)

REM 检查Python是否可用
echo [%datestamp% %HH%:%Min%:%Sec%] 检查Python版本... >> "%LOG_FILE%"
"D:\Program Files\Python\python.exe" --version >> "%LOG_FILE%" 2>&1
if errorlevel 1 (
    echo [%datestamp% %HH%:%Min%:%Sec%] ERROR: Python不可用或版本检查失败 >> "%LOG_FILE%"
    echo Python不可用或版本检查失败
    echo 按任意键继续...
    pause >nul
    exit /b 1
)

REM 检查必要的目录是否存在
echo [%datestamp% %HH%:%Min%:%Sec%] 检查项目目录... >> "%LOG_FILE%"
set MISSING_DIRS=
if not exist "APPLE" set MISSING_DIRS=%MISSING_DIRS% APPLE
if not exist "HUAWEI" set MISSING_DIRS=%MISSING_DIRS% HUAWEI
if not exist "OPPO" set MISSING_DIRS=%MISSING_DIRS% OPPO
if not exist "VIVO" set MISSING_DIRS=%MISSING_DIRS% VIVO
if not exist "XIAOMI" set MISSING_DIRS=%MISSING_DIRS% XIAOMI
if not exist "HONOR" set MISSING_DIRS=%MISSING_DIRS% HONOR

if not "%MISSING_DIRS%"=="" (
    echo [%datestamp% %HH%:%Min%:%Sec%] WARNING: 以下目录不存在:%MISSING_DIRS% >> "%LOG_FILE%"
    echo 警告: 以下目录不存在:%MISSING_DIRS%
)

REM 检查send_email.py是否存在
if not exist "send_email.py" (
    echo [%datestamp% %HH%:%Min%:%Sec%] ERROR: send_email.py文件不存在 >> "%LOG_FILE%"
    echo send_email.py文件不存在
    echo 按任意键继续...
    pause >nul
    exit /b 1
)

REM 杀死可能残留的Chrome进程
echo [%datestamp% %HH%:%Min%:%Sec%] 清理残留进程... >> "%LOG_FILE%"
taskkill /f /im chrome.exe /t >nul 2>&1
taskkill /f /im chromedriver.exe /t >nul 2>&1

REM 等待进程完全结束
timeout /t 3 /nobreak >nul

REM 执行Python脚本
echo [%datestamp% %HH%:%Min%:%Sec%] 开始执行Python脚本... >> "%LOG_FILE%"
echo 正在执行Python脚本，请稍候...
"D:\Program Files\Python\python.exe" "%~dp0send_email.py" >> "%LOG_FILE%" 2>&1

REM 获取退出码
set PYTHON_EXIT_CODE=!errorlevel!

REM 记录结果
if !PYTHON_EXIT_CODE! equ 0 (
    echo [%datestamp% %HH%:%Min%:%Sec%] Python脚本执行成功 >> "%LOG_FILE%"
    echo [%datestamp% %HH%:%Min%:%Sec%] ========== 计划任务执行完成 ========== >> "%LOG_FILE%"
    echo Python脚本执行成功！
) else (
    echo [%datestamp% %HH%:%Min%:%Sec%] Python脚本执行失败，退出码: !PYTHON_EXIT_CODE! >> "%LOG_FILE%"
    echo [%datestamp% %HH%:%Min%:%Sec%] ========== 计划任务执行失败 ========== >> "%LOG_FILE%"
    echo Python脚本执行失败，退出码: !PYTHON_EXIT_CODE!
    echo 请查看日志文件: %LOG_FILE%
)

REM 清理残留进程
echo [%datestamp% %HH%:%Min%:%Sec%] 清理残留进程... >> "%LOG_FILE%"
taskkill /f /im chrome.exe /t >nul 2>&1
taskkill /f /im chromedriver.exe /t >nul 2>&1

REM 显示日志文件位置
echo.
echo 日志文件位置: %LOG_FILE%
echo.

REM 如果是手动执行，等待用户按键
if /i "%1"=="manual" (
    echo 按任意键退出...
    pause >nul
) else if /i "%1"=="" (
    echo 按任意键退出...
    pause >nul
)

REM 返回Python脚本的退出码
exit /b !PYTHON_EXIT_CODE!
