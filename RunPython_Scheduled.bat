@echo off
REM 专门用于Windows计划任务的Python自动化脚本启动器
REM 解决计划任务中Chrome WebDriver启动问题

cd /d "%~dp0"

REM 设置关键环境变量
set PYTHONIOENCODING=utf-8
set PYTHONUNBUFFERED=1
set DISPLAY=:0
set CHROME_NO_SANDBOX=1

REM 设置日志文件路径
set LOG_DIR=%~dp0logs
set LOG_FILE=%LOG_DIR%\scheduled_task_%date:~0,4%-%date:~5,2%-%date:~8,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%.log

REM 创建日志目录
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

REM 记录开始时间
echo [%date% %time%] ========== 计划任务开始执行 ========== >> "%LOG_FILE%"
echo [%date% %time%] 工作目录: %CD% >> "%LOG_FILE%"
echo [%date% %time%] Python路径: "D:\Program Files\Python\python.exe" >> "%LOG_FILE%"

REM 检查Python是否可用
"D:\Program Files\Python\python.exe" --version >> "%LOG_FILE%" 2>&1
if errorlevel 1 (
    echo [%date% %time%] ERROR: Python不可用 >> "%LOG_FILE%"
    exit /b 1
)

REM 检查必要的目录是否存在
if not exist "APPLE" (
    echo [%date% %time%] ERROR: APPLE目录不存在 >> "%LOG_FILE%"
    exit /b 1
)

REM 杀死可能残留的Chrome进程
taskkill /f /im chrome.exe /t >nul 2>&1
taskkill /f /im chromedriver.exe /t >nul 2>&1

REM 等待进程完全结束
timeout /t 3 /nobreak >nul

REM 执行Python脚本
echo [%date% %time%] 开始执行Python脚本... >> "%LOG_FILE%"
"D:\Program Files\Python\python.exe" "%~dp0send_email.py" >> "%LOG_FILE%" 2>&1

REM 获取退出码
set PYTHON_EXIT_CODE=%errorlevel%

REM 记录结果
if %PYTHON_EXIT_CODE% equ 0 (
    echo [%date% %time%] Python脚本执行成功 >> "%LOG_FILE%"
    echo [%date% %time%] ========== 计划任务执行完成 ========== >> "%LOG_FILE%"
) else (
    echo [%date% %time%] Python脚本执行失败，退出码: %PYTHON_EXIT_CODE% >> "%LOG_FILE%"
    echo [%date% %time%] ========== 计划任务执行失败 ========== >> "%LOG_FILE%"
)

REM 清理残留进程
taskkill /f /im chrome.exe /t >nul 2>&1
taskkill /f /im chromedriver.exe /t >nul 2>&1

REM 返回Python脚本的退出码
exit /b %PYTHON_EXIT_CODE%
