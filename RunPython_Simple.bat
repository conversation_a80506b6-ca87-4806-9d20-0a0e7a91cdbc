@echo off
REM 简化版计划任务脚本 - 解决闪退问题

REM 设置错误处理
setlocal enabledelayedexpansion

REM 强制切换到脚本所在目录
pushd "%~dp0"

REM 设置基本环境变量
set PYTHONIOENCODING=utf-8
set PYTHONUNBUFFERED=1

REM 创建日志目录和文件
if not exist "logs" mkdir "logs"
set LOG_FILE=logs\task_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%.log
set LOG_FILE=%LOG_FILE: =0%

REM 记录开始信息
echo [%date% %time%] 任务开始 > "%LOG_FILE%"
echo [%date% %time%] 工作目录: %CD% >> "%LOG_FILE%"
echo [%date% %time%] 脚本目录: %~dp0 >> "%LOG_FILE%"

REM 基本检查
if not exist "send_email.py" (
    echo [%date% %time%] 错误: send_email.py不存在 >> "%LOG_FILE%"
    echo send_email.py文件不存在于目录: %CD%
    if "%1"=="" (
        echo 按任意键退出...
        pause >nul
    )
    exit /b 1
)

if not exist "D:\Program Files\Python\python.exe" (
    echo [%date% %time%] 错误: Python不存在 >> "%LOG_FILE%"
    echo Python不存在于: D:\Program Files\Python\python.exe
    if "%1"=="" (
        echo 按任意键退出...
        pause >nul
    )
    exit /b 1
)

REM 清理可能的残留进程
taskkill /f /im chrome.exe >nul 2>&1
taskkill /f /im chromedriver.exe >nul 2>&1

REM 执行Python脚本
echo [%date% %time%] 开始执行Python脚本 >> "%LOG_FILE%"
"D:\Program Files\Python\python.exe" "send_email.py" >> "%LOG_FILE%" 2>&1
set EXIT_CODE=!errorlevel!

REM 记录结果
if !EXIT_CODE! equ 0 (
    echo [%date% %time%] 执行成功 >> "%LOG_FILE%"
    echo 任务执行成功！
) else (
    echo [%date% %time%] 执行失败，退出码: !EXIT_CODE! >> "%LOG_FILE%"
    echo 任务执行失败，退出码: !EXIT_CODE!
    echo 查看日志: %LOG_FILE%
)

REM 清理残留进程
taskkill /f /im chrome.exe >nul 2>&1
taskkill /f /im chromedriver.exe >nul 2>&1

REM 如果是手动执行，显示结果
if "%1"=="" (
    echo.
    echo 日志文件: %LOG_FILE%
    echo 按任意键退出...
    pause >nul
)

REM 恢复目录并退出
popd
exit /b !EXIT_CODE!
